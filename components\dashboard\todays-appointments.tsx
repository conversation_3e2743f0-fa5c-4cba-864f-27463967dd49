"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Clock, User, MapPin } from "lucide-react"
import { useAuth } from "@/lib/auth-provider"
import { useRealTimeEvent } from "@/hooks/use-real-time-updates"
import { RealTimeEventType } from "@/lib/real-time-service"
import { getAllAppointments } from "@/lib/appointment-service"
import { useLocations } from "@/lib/location-provider"
import { format, isToday, parseISO } from "date-fns"

export function TodaysAppointments() {
  const { currentLocation } = useAuth()
  const { getLocationName } = useLocations()
  const [refreshKey, setRefreshKey] = useState(0)

  // Get today's appointments
  const appointments = useMemo(() => {
    try {
      const allAppointments = getAllAppointments()

      return allAppointments
        .filter(appointment => {
          // Filter by today's date
          const appointmentDate = new Date(appointment.date)
          if (!isToday(appointmentDate)) return false

          // Filter by location if not "all"
          if (currentLocation !== 'all' && appointment.location !== currentLocation) {
            return false
          }

          return true
        })
        .sort((a, b) => {
          // Sort by time
          const timeA = new Date(a.date).getTime()
          const timeB = new Date(b.date).getTime()
          return timeA - timeB
        })
        .slice(0, 5) // Show max 5 appointments
        .map(appointment => {
          // Format the appointment data
          const appointmentDate = new Date(appointment.date)
          const timeString = format(appointmentDate, 'h:mm a')

          // Get service name
          let serviceName = 'Service'
          if (appointment.services && appointment.services.length > 0) {
            serviceName = appointment.services.length === 1
              ? appointment.services[0].name
              : `${appointment.services.length} services`
          } else if (appointment.service) {
            serviceName = appointment.service
          }

          // Get stylist info
          const stylistName = appointment.staffName || appointment.staff || 'Staff'
          const stylistInitials = stylistName.split(' ').map(n => n[0]).join('').toUpperCase()

          // Get status badge color
          const getStatusColor = (status: string) => {
            switch (status?.toLowerCase()) {
              case 'confirmed':
                return 'bg-blue-100 text-blue-800'
              case 'checked in':
              case 'checked_in':
                return 'bg-green-100 text-green-800'
              case 'completed':
                return 'bg-green-100 text-green-800'
              case 'cancelled':
                return 'bg-red-100 text-red-800'
              case 'no show':
              case 'no_show':
                return 'bg-gray-100 text-gray-800'
              default:
                return 'bg-yellow-100 text-yellow-800'
            }
          }

          return {
            id: appointment.id,
            client: appointment.clientName || 'Unknown Client',
            service: serviceName,
            time: timeString,
            stylist: stylistName,
            stylistId: stylistInitials,
            stylistColor: getStatusColor(appointment.status),
            location: getLocationName(appointment.location) || appointment.location,
            status: appointment.status || 'Scheduled',
          }
        })
    } catch (error) {
      console.error('Error loading today\'s appointments:', error)
      return []
    }
  }, [currentLocation, getLocationName, refreshKey])

  // Real-time event listeners for automatic updates
  useRealTimeEvent(RealTimeEventType.APPOINTMENT_CREATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  useRealTimeEvent(RealTimeEventType.APPOINTMENT_UPDATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  useRealTimeEvent(RealTimeEventType.APPOINTMENT_STATUS_CHANGED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-bold">Today's Appointments</CardTitle>
        <p className="text-sm text-muted-foreground">
          You have {appointments.length} appointments scheduled for today.
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {appointments.length > 0 ? (
            appointments.map((appointment) => (
              <div
                key={appointment.id}
                className="flex flex-col md:flex-row md:items-center justify-between border-b pb-4"
              >
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{appointment.client}</h4>
                    <Badge
                      className={
                        appointment.status === "Checked In" || appointment.status === "checked_in"
                          ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"
                          : appointment.status === "completed"
                          ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"
                          : appointment.status === "cancelled"
                          ? "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800"
                          : "bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800"
                      }
                    >
                      {appointment.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{appointment.time}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>{appointment.service}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>{appointment.location}</span>
                  </div>
                </div>

                <div className="flex items-center gap-4 mt-4 md:mt-0">
                  <div className="flex items-center gap-2">
                    <Avatar className={`h-8 w-8 ${appointment.stylistColor}`}>
                      <AvatarFallback>{appointment.stylistId}</AvatarFallback>
                    </Avatar>
                    <div className="text-sm">
                      <p>{appointment.stylist}</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Details
                    </Button>
                    <Button size="sm">Check In</Button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-muted-foreground py-8">
              No appointments scheduled for today
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

